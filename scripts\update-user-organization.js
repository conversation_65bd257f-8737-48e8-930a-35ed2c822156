#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to update a user's organization_id
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Supabase configuration
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://qcnotlojmyvpqbbgoxbc.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

// Initialize Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function updateUserOrganization() {
  const userId = '7b014086-240c-49d4-9e5c-58836ab28f08';
  const newOrganizationId = 'c564c8d8-469e-4b64-a0d3-62a68c379ec1';

  try {
    console.log('🔍 Checking current user details...');

    // First, check the current user details
    const { data: currentUser, error: fetchError } = await supabase
      .from('profiles')
      .select('id, email, first_name, last_name, role, organization_id, account_type')
      .eq('id', userId)
      .single();

    if (fetchError) {
      console.error('❌ Error fetching user:', fetchError);
      return;
    }

    if (!currentUser) {
      console.error('❌ User not found with ID:', userId);
      return;
    }

    console.log('✅ Current user details:');
    console.log(`   - ID: ${currentUser.id}`);
    console.log(`   - Email: ${currentUser.email}`);
    console.log(`   - Name: ${currentUser.first_name} ${currentUser.last_name}`);
    console.log(`   - Role: ${currentUser.role}`);
    console.log(`   - Current Organization ID: ${currentUser.organization_id}`);
    console.log(`   - Account Type: ${currentUser.account_type}`);

    // Check if the new organization exists
    console.log('\n🔍 Checking target organization...');
    const { data: targetOrg, error: orgError } = await supabase
      .from('organizations')
      .select('id, name, type')
      .eq('id', newOrganizationId)
      .single();

    if (orgError) {
      console.error('❌ Error fetching target organization:', orgError);
      return;
    }

    if (!targetOrg) {
      console.error('❌ Target organization not found with ID:', newOrganizationId);
      return;
    }

    console.log('✅ Target organization details:');
    console.log(`   - ID: ${targetOrg.id}`);
    console.log(`   - Name: ${targetOrg.name}`);
    console.log(`   - Type: ${targetOrg.type}`);

    // Confirm the update
    console.log('\n🔄 Updating user organization...');
    
    // Update the user's organization_id
    const { data: updatedUser, error: updateError } = await supabase
      .from('profiles')
      .update({ organization_id: newOrganizationId })
      .eq('id', userId)
      .select('id, email, first_name, last_name, role, organization_id')
      .single();

    if (updateError) {
      console.error('❌ Error updating user organization:', updateError);
      return;
    }

    console.log('✅ User organization updated successfully!');
    console.log('📊 Updated user details:');
    console.log(`   - ID: ${updatedUser.id}`);
    console.log(`   - Email: ${updatedUser.email}`);
    console.log(`   - Name: ${updatedUser.first_name} ${updatedUser.last_name}`);
    console.log(`   - Role: ${updatedUser.role}`);
    console.log(`   - New Organization ID: ${updatedUser.organization_id}`);

    // Verify the change
    console.log('\n🔍 Verifying the update...');
    const { data: verifyUser, error: verifyError } = await supabase
      .from('profiles')
      .select('id, email, organization_id')
      .eq('id', userId)
      .single();

    if (verifyError) {
      console.error('❌ Error verifying update:', verifyError);
      return;
    }

    if (verifyUser.organization_id === newOrganizationId) {
      console.log('✅ Update verified successfully!');
      console.log(`   User ${verifyUser.email} is now in organization ${newOrganizationId}`);
    } else {
      console.error('❌ Update verification failed!');
      console.log(`   Expected: ${newOrganizationId}`);
      console.log(`   Actual: ${verifyUser.organization_id}`);
    }

  } catch (error) {
    console.error('❌ Script failed:', error);
  }
}

// Run the script
updateUserOrganization();
