import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  <PERSON><PERSON>T<PERSON>le,
  DialogTrigger
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { XCircle, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { Task } from '@/types/tasks';

interface CancelTaskActionsProps {
  task: Task;
  onTaskUpdated: () => void;
}

const CancelTaskActions: React.FC<CancelTaskActionsProps> = ({ task, onTaskUpdated }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [reason, setReason] = useState('');
  const [isCancelling, setIsCancelling] = useState(false);
  const { toast } = useToast();

  const handleCancel = async () => {
    if (!reason.trim()) {
      toast({
        title: "Reason required",
        description: "Please provide a reason for cancelling this task.",
        variant: "destructive",
      });
      return;
    }

    setIsCancelling(true);

    try {
      // Update task status to cancelled
      const { error: updateError } = await supabase
        .from('tasks')
        .update({
          status: 'cancelled',
          updated_at: new Date().toISOString()
        })
        .eq('id', task.id);

      if (updateError) {
        throw updateError;
      }

      // If task has offers, reject all pending offers
      if (task.offers_count > 0) {
        const { error: offersError } = await supabase
          .from('offers')
          .update({ status: 'rejected' })
          .eq('task_id', task.id)
          .in('status', ['pending', 'submitted']);

        if (offersError) {
          console.error('Error rejecting offers:', offersError);
          // Don't throw here as the main task cancellation succeeded
        }
      }

      toast({
        title: "Task cancelled",
        description: "The task has been successfully cancelled.",
      });

      setIsOpen(false);
      setReason('');
      onTaskUpdated();
    } catch (error: any) {
      console.error('Error cancelling task:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to cancel task. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsCancelling(false);
    }
  };

  // Don't show cancel button for already completed, closed, or cancelled tasks
  if (['completed', 'closed', 'confirmed', 'cancelled', 'pending_payment'].includes(task.status)) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="destructive" className="w-full">
          <XCircle className="h-4 w-4 mr-2" />
          Cancel Task
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Cancel Task</DialogTitle>
          <DialogDescription>
            Are you sure you want to cancel this task? This action cannot be undone.
            {task.offers_count > 0 && (
              <span className="block mt-2 text-amber-600">
                Note: All pending offers will be automatically rejected.
              </span>
            )}
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="reason">Reason for cancellation *</Label>
            <Textarea
              id="reason"
              placeholder="Please provide a reason for cancelling this task..."
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              className="min-h-[100px]"
            />
          </div>
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => setIsOpen(false)}
            disabled={isCancelling}
          >
            Keep Task
          </Button>
          <Button
            variant="destructive"
            onClick={handleCancel}
            disabled={isCancelling || !reason.trim()}
          >
            {isCancelling ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Cancelling...
              </>
            ) : (
              <>
                <XCircle className="h-4 w-4 mr-2" />
                Cancel Task
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CancelTaskActions;
