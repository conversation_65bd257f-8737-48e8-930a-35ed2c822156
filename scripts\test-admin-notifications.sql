-- SQL Test Script for Admin Notification System
-- Run this in Supabase SQL Editor to test the admin notification flow

-- Step 1: Get teacher profile
SELECT 
  'Step 1: Teacher Profile' as step,
  id,
  email,
  first_name,
  last_name,
  role,
  organization_id
FROM profiles 
WHERE '<EMAIL>' = ANY(email);

-- Step 2: Get admin users in the same organization
SELECT 
  'Step 2: Admin Users' as step,
  id,
  email,
  first_name,
  last_name,
  role
FROM profiles 
WHERE organization_id = (
  SELECT organization_id 
  FROM profiles 
  WHERE '<EMAIL>' = ANY(email)
)
AND role = 'admin';

-- Step 3: Create test task (using DO block for transaction)
DO $$
DECLARE
  teacher_id UUID;
  teacher_org_id UUID;
  teacher_name TEXT;
  new_task_id UUID;
  admin_user RECORD;
  notification_id UUID;
BEGIN
  -- Get teacher info
  SELECT id, organization_id, CONCAT(first_name, ' ', last_name) 
  INTO teacher_id, teacher_org_id, teacher_name
  FROM profiles 
  WHERE '<EMAIL>' = ANY(email);
  
  IF teacher_id IS NULL THEN
    RAISE EXCEPTION 'Teacher not found';
  END IF;
  
  RAISE NOTICE 'Found teacher: % (ID: %, Org: %)', teacher_name, teacher_id, teacher_org_id;
  
  -- Create test task
  INSERT INTO tasks (
    title,
    description,
    user_id,
    organization_id,
    status,
    visibility,
    category
  ) VALUES (
    'SQL Test - Admin Notifications',
    'Test task created by SQL script to test admin notifications',
    teacher_id,
    teacher_org_id,
    'open',
    'admin',
    'Testing'
  ) RETURNING id INTO new_task_id;
  
  RAISE NOTICE 'Created task: %', new_task_id;
  
  -- Create creator notification
  INSERT INTO notifications (
    user_id,
    type,
    message,
    related_id,
    related_type,
    read,
    email_sent
  ) VALUES (
    teacher_id,
    'task_update',
    'Your task "SQL Test - Admin Notifications" has been created successfully.',
    new_task_id,
    'task',
    false,
    false
  ) RETURNING id INTO notification_id;
  
  RAISE NOTICE 'Created creator notification: %', notification_id;
  
  -- Create admin notifications for each admin user
  FOR admin_user IN 
    SELECT id, first_name, last_name 
    FROM profiles 
    WHERE organization_id = teacher_org_id 
    AND role = 'admin'
  LOOP
    INSERT INTO notifications (
      user_id,
      type,
      message,
      related_id,
      related_type,
      read,
      email_sent
    ) VALUES (
      admin_user.id,
      'admin_review',
      'A new task "SQL Test - Admin Notifications" has been created by ' || teacher_name || ' and is awaiting admin review.',
      new_task_id,
      'task',
      false,
      false
    ) RETURNING id INTO notification_id;
    
    RAISE NOTICE 'Created admin notification for % %: %', admin_user.first_name, admin_user.last_name, notification_id;
  END LOOP;
  
  RAISE NOTICE 'Test completed successfully!';
  
END $$;

-- Step 4: Verify notifications were created
SELECT 
  'Step 4: Verification' as step,
  n.id,
  n.type,
  n.message,
  n.read,
  n.email_sent,
  n.created_at,
  p.email,
  p.first_name,
  p.last_name,
  p.role
FROM notifications n
JOIN profiles p ON n.user_id = p.id
WHERE n.related_id IN (
  SELECT id FROM tasks 
  WHERE title = 'SQL Test - Admin Notifications'
  AND created_at >= NOW() - INTERVAL '1 hour'
)
ORDER BY n.created_at DESC;

-- Step 5: Count notifications by type
SELECT 
  'Step 5: Summary' as step,
  n.type,
  COUNT(*) as count
FROM notifications n
WHERE n.related_id IN (
  SELECT id FROM tasks 
  WHERE title = 'SQL Test - Admin Notifications'
  AND created_at >= NOW() - INTERVAL '1 hour'
)
GROUP BY n.type;

-- Clean up (optional - uncomment to remove test data)
/*
DELETE FROM notifications 
WHERE related_id IN (
  SELECT id FROM tasks 
  WHERE title = 'SQL Test - Admin Notifications'
  AND created_at >= NOW() - INTERVAL '1 hour'
);

DELETE FROM tasks 
WHERE title = 'SQL Test - Admin Notifications'
AND created_at >= NOW() - INTERVAL '1 hour';
*/
