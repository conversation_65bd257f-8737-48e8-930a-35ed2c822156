-- Update user organization_id
-- User ID: 7b014086-240c-49d4-9e5c-58836ab28f08
-- New Organization ID: c564c8d8-469e-4b64-a0d3-62a68c379ec1

-- First, check the current user details
SELECT 
  'Current user details:' as info,
  id,
  email,
  first_name,
  last_name,
  role,
  organization_id,
  account_type
FROM profiles 
WHERE id = '7b014086-240c-49d4-9e5c-58836ab28f08';

-- Check the target organization exists
SELECT 
  'Target organization details:' as info,
  id,
  name,
  type
FROM organizations 
WHERE id = 'c564c8d8-469e-4b64-a0d3-62a68c379ec1';

-- Update the user's organization_id
UPDATE profiles 
SET organization_id = 'c564c8d8-469e-4b64-a0d3-62a68c379ec1'
WHERE id = '7b014086-240c-49d4-9e5c-58836ab28f08';

-- Verify the update
SELECT 
  'Updated user details:' as info,
  id,
  email,
  first_name,
  last_name,
  role,
  organization_id,
  account_type
FROM profiles 
WHERE id = '7b014086-240c-49d4-9e5c-58836ab28f08';
